package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.MnjxAgentPassenger;
import com.swcares.entity.MnjxAgentPassengerCard;
import com.swcares.entity.MnjxAgentPassengerDocument;
import com.swcares.entity.MnjxAgentPassengerRemark;
import com.swcares.obj.dto.AgencyFrequentAddDto;
import com.swcares.obj.dto.AgencyFrequentDeleteDto;
import com.swcares.obj.dto.AgencyFrequentQueryDto;
import com.swcares.obj.vo.AgencyFrequentQueryVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IAgencyFrequentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 代理常客服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/04 15:30
 */
@Slf4j
@Service
public class AgencyFrequentServiceImpl implements IAgencyFrequentService {

    @Resource
    private IMnjxAgentPassengerService iMnjxAgentPassengerService;

    @Resource
    private IMnjxAgentPassengerDocumentService iMnjxAgentPassengerDocumentService;

    @Resource
    private IMnjxAgentPassengerRemarkService iMnjxAgentPassengerRemarkService;

    @Resource
    private IMnjxAgentPassengerCardService iMnjxAgentPassengerCardService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    /**
     * 中文姓名正则表达式
     */
    private static final String CHINESE_NAME_PATTERN = "^[\\u4e00-\\u9fa5]+$";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addAgencyFrequent(AgencyFrequentAddDto dto) throws SguiResultException {
        // 1. 数据验证
        this.validateData(dto);

        // 2. 获取当前登录用户的工作号ID
        String currentSiId = this.getCurrentSiId();

        // 4. 保存主表数据
        MnjxAgentPassenger agentPassenger = this.saveAgentPassenger(dto, currentSiId);

        // 5. 保存证件信息
        this.saveDocuments(dto.getDocumentList(), agentPassenger.getAgentPassengerId());

        // 6. 保存备注信息
        if (dto.getRemarkList() != null && !dto.getRemarkList().isEmpty()) {
            this.saveRemarks(dto.getRemarkList(), agentPassenger.getAgentPassengerId());
        }

        // 7. 保存常旅客卡信息
        if (dto.getCardList() != null && !dto.getCardList().isEmpty()) {
            this.saveCards(dto.getCardList(), agentPassenger.getAgentPassengerId());
        }

        log.info("成功新建代理常客，ID：{}，客户编号：{}", agentPassenger.getAgentPassengerId(), dto.getCsmIdentifier());

        return "success";
    }

    /**
     * 数据验证
     */
    private void validateData(AgencyFrequentAddDto dto) throws SguiResultException {
        // 验证中文姓名格式（根据需求要求）
        if (CharSequenceUtil.isNotBlank(dto.getChineseName()) &&
                !Pattern.matches(CHINESE_NAME_PATTERN, dto.getChineseName())) {
            throw new SguiResultException("中文姓名格式不正确，只能包含中文字符");
        }

        // 验证csmIdentifier范围（根据需求要求）
        if (dto.getCsmIdentifier() == null || dto.getCsmIdentifier() < 0 || dto.getCsmIdentifier() > 9999) {
            throw new SguiResultException("客户ID范围必须在0-9999之间");
        }

        // 验证证件列表不为空（根据需求要求）
        if (dto.getDocumentList() == null || dto.getDocumentList().isEmpty()) {
            throw new SguiResultException("证件列表至少要有一条数据");
        }

        // 客户级别只能输入一位
        if (dto.getFrequentFlyerLevelNumberInfo().length() != 1) {
            throw new SguiResultException("客户级别只能输入一位");
        }
    }

    /**
     * 获取当前登录用户的工作号ID
     */
    private String getCurrentSiId() throws SguiResultException {
        String siId = iSguiCommonService.getCurrentUserInfo().getSiId();
        if (CharSequenceUtil.isBlank(siId)) {
            throw new SguiResultException("获取当前用户信息失败");
        }
        return siId;
    }

    /**
     * 保存代理常客主表数据
     */
    private MnjxAgentPassenger saveAgentPassenger(AgencyFrequentAddDto dto, String siId) throws SguiResultException {
        // 查询该siId下的所有agentPassenger数据
        List<MnjxAgentPassenger> agentPassengerList = iMnjxAgentPassengerService.lambdaQuery()
                .eq(MnjxAgentPassenger::getSiId, siId)
                .orderByAsc(MnjxAgentPassenger::getCsmIdentifier)
                .list();
        // 如果没有数据，csmIdentifier从0开始。如果有数据，按顺序从小到大获取下一个csmIdentifier，中间有删除空缺的继续使用
        int csmIdentifier = 0;
        if (CollUtil.isNotEmpty(agentPassengerList)) {
            List<Integer> csmIdentifiers = agentPassengerList.stream()
                    .map(MnjxAgentPassenger::getCsmIdentifier)
                    .sorted()
                    .collect(Collectors.toList());
            boolean find = false;
            for (int i = 0; i < csmIdentifiers.size(); i++) {
                if (csmIdentifiers.get(i) != i) {
                    csmIdentifier = i;
                    find = true;
                    break;
                }
            }
            // 如果循环结束还没有找到空缺的，就使用最大的+1
            if (csmIdentifier == 0 && !find) {
                csmIdentifier = csmIdentifiers.get(csmIdentifiers.size() - 1) + 1;
            }
        }
        if (csmIdentifier > 9999) {
            throw new SguiResultException("无法继续添加代理常客");
        }
        MnjxAgentPassenger agentPassenger = this.constructAgentPassenger(dto, siId, csmIdentifier);

        iMnjxAgentPassengerService.save(agentPassenger);
        return agentPassenger;
    }

    private MnjxAgentPassenger constructAgentPassenger(AgencyFrequentAddDto dto, String siId, int csmIdentifier) {
        MnjxAgentPassenger agentPassenger = new MnjxAgentPassenger();
        agentPassenger.setSiId(siId);
        agentPassenger.setCsmIdentifier(csmIdentifier);
        agentPassenger.setChineseName(dto.getChineseName());
        agentPassenger.setEnglishName(dto.getEnglishName());
        agentPassenger.setGender(dto.getGenderCode());
        agentPassenger.setBirthDate(dto.getBirthDate());
        agentPassenger.setMobilePhone(dto.getMobilePhone());
        agentPassenger.setEmailAddress(dto.getEmailAddress());
        agentPassenger.setFrequentFlyerNumberInfo(dto.getFrequentFlyerNumberInfo());
        agentPassenger.setFrequentFlyerLevelNumberInfo(dto.getFrequentFlyerLevelNumberInfo());
        return agentPassenger;
    }

    /**
     * 保存证件信息
     */
    private void saveDocuments(List<AgencyFrequentAddDto.DocumentInfo> documentList, String agentPassengerId) {
        for (AgencyFrequentAddDto.DocumentInfo docInfo : documentList) {
            MnjxAgentPassengerDocument document = new MnjxAgentPassengerDocument();
            document.setAgentPassengerId(agentPassengerId);
            document.setDocumentTypeCode(docInfo.getDocumentTypeCode());
            document.setDocumentNumber(docInfo.getDocumentNumber());
            document.setExpiryDate(docInfo.getExpiryDate());
            document.setIssueCountryCode(docInfo.getIssueCountryCode());
            document.setNationalityCode(docInfo.getNationalityCode());
            document.setIssueDate(docInfo.getIssueDate());

            iMnjxAgentPassengerDocumentService.save(document);
        }
    }

    /**
     * 保存备注信息
     */
    private void saveRemarks(List<AgencyFrequentAddDto.RemarkInfo> remarkList, String agentPassengerId) {
        for (AgencyFrequentAddDto.RemarkInfo remarkInfo : remarkList) {
            MnjxAgentPassengerRemark remark = new MnjxAgentPassengerRemark();
            remark.setAgentPassengerId(agentPassengerId);
            remark.setSequenceNumber(remarkInfo.getSequenceNumber());
            remark.setRemark(remarkInfo.getRemark());

            iMnjxAgentPassengerRemarkService.save(remark);
        }
    }

    /**
     * 保存常旅客卡信息
     */
    private void saveCards(List<AgencyFrequentAddDto.CardInfo> cardList, String agentPassengerId) {
        for (AgencyFrequentAddDto.CardInfo cardInfo : cardList) {
            MnjxAgentPassengerCard card = new MnjxAgentPassengerCard();
            card.setAgentPassengerId(agentPassengerId);
            card.setAirlineCode(cardInfo.getAirlineCode());
            card.setFrequentFlyerNumber(cardInfo.getFrequentFlyerNumber());

            iMnjxAgentPassengerCardService.save(card);
        }
    }

    @Override
    public AgencyFrequentQueryVo queryAgencyFrequent(AgencyFrequentQueryDto dto) throws SguiResultException {
        // 1. 请求验证
        this.validateQueryData(dto);

        // 2. 获取当前登录用户的工作号ID
        String currentSiId = this.getCurrentSiId();

        // 3. 查询代理常客主表数据
        List<MnjxAgentPassenger> passengerList = this.queryAgentPassengers(dto, currentSiId);
        if (passengerList == null || passengerList.isEmpty()) {
            return null;
        }

        // 4. 构建返回数据
        AgencyFrequentQueryVo result = new AgencyFrequentQueryVo();
        List<AgencyFrequentQueryVo.AgencyFrequentInfo> resultList = new ArrayList<>();

        for (MnjxAgentPassenger passenger : passengerList) {
            // 验证证件号码匹配
            if (CharSequenceUtil.isNotEmpty(dto.getDocumentNumber()) && !this.validateDocumentNumber(passenger.getAgentPassengerId(), dto.getDocumentNumber())) {
                continue;
            }

            AgencyFrequentQueryVo.AgencyFrequentInfo info = this.buildAgencyFrequentInfo(passenger);
            resultList.add(info);
        }

        if (resultList.isEmpty()) {
            return null;
        }

        result.setResult(resultList);
        return result;
    }

    /**
     * 查询请求数据验证
     */
    private void validateQueryData(AgencyFrequentQueryDto dto) throws SguiResultException {
        // 如果documentNumber为空，则passengerName、csmIdentifier、mobilePhone不能全部为空
        if (CharSequenceUtil.isBlank(dto.getDocumentNumber())) {
            if (CharSequenceUtil.isAllEmpty(dto.getPassengerName(), dto.getCsmIdentifier(), dto.getMobilePhone())) {
                throw new SguiResultException("旅客姓名、客户编号、手机号不能全部为空");
            }
        } else {
            // 如果documentNumber不为空，则按原逻辑验证
            if (CharSequenceUtil.isBlank(dto.getPassengerName())) {
                throw new SguiResultException("旅客姓名不能为空");
            }
        }
    }

    /**
     * 查询代理常客主表数据
     */
    private List<MnjxAgentPassenger> queryAgentPassengers(AgencyFrequentQueryDto dto, String siId) {
        LambdaQueryWrapper<MnjxAgentPassenger> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MnjxAgentPassenger::getSiId, siId);

        // 如果csmIdentifier不为空，添加查询条件
        if (CharSequenceUtil.isNotBlank(dto.getCsmIdentifier())) {
            try {
                Integer csmId = Integer.valueOf(dto.getCsmIdentifier());
                wrapper.eq(MnjxAgentPassenger::getCsmIdentifier, csmId);
            } catch (NumberFormatException e) {
                log.warn("客户编号格式不正确：{}", dto.getCsmIdentifier());
                return new ArrayList<>();
            }
        }

        // 如果mobilePhone不为空，添加查询条件
        if (CharSequenceUtil.isNotBlank(dto.getMobilePhone())) {
            wrapper.eq(MnjxAgentPassenger::getMobilePhone, dto.getMobilePhone());
        }

        // 如果passengerName不为空，添加查询条件
        if (CharSequenceUtil.isNotBlank(dto.getPassengerName())) {
            wrapper.eq(MnjxAgentPassenger::getChineseName, dto.getPassengerName());
        }

        return iMnjxAgentPassengerService.list(wrapper);
    }

    /**
     * 验证证件号码是否匹配
     */
    private boolean validateDocumentNumber(String agentPassengerId, String documentNumber) {
        long count = iMnjxAgentPassengerDocumentService.lambdaQuery()
                .eq(MnjxAgentPassengerDocument::getAgentPassengerId, agentPassengerId)
                .eq(MnjxAgentPassengerDocument::getDocumentNumber, documentNumber)
                .count();
        return count > 0;
    }

    /**
     * 构建代理常客信息
     */
    private AgencyFrequentQueryVo.AgencyFrequentInfo buildAgencyFrequentInfo(MnjxAgentPassenger passenger) {
        AgencyFrequentQueryVo.AgencyFrequentInfo info = new AgencyFrequentQueryVo.AgencyFrequentInfo();

        // 基本信息
        info.setCsmIdentifier(passenger.getCsmIdentifier());
        info.setChineseName(passenger.getChineseName());
        info.setEnglishName(passenger.getEnglishName());
        info.setGenderCode(passenger.getGender());
        info.setBirthDate(passenger.getBirthDate());
        info.setMobilePhone(passenger.getMobilePhone());
        info.setMobilePhoneDes(this.maskMobilePhone(passenger.getMobilePhone()));
        info.setEmailAddress(passenger.getEmailAddress());
        info.setFrequentFlyerNumberInfo(passenger.getFrequentFlyerNumberInfo());
        info.setFrequentFlyerLevelNumberInfo(passenger.getFrequentFlyerLevelNumberInfo());

        // 证件信息
        info.setDocumentList(this.buildDocumentList(passenger.getAgentPassengerId()));

        // 备注信息
        info.setRemarkList(this.buildRemarkList(passenger.getAgentPassengerId()));

        // 常旅客卡信息
        info.setCardList(this.buildCardList(passenger.getAgentPassengerId()));

        return info;
    }

    /**
     * 手机号脱敏处理
     */
    private String maskMobilePhone(String mobilePhone) {
        if (CharSequenceUtil.isBlank(mobilePhone) || mobilePhone.length() < 7) {
            return mobilePhone;
        }
        return mobilePhone.substring(0, 3) + "****" + mobilePhone.substring(mobilePhone.length() - 2);
    }

    /**
     * 证件号脱敏处理
     */
    private String maskDocumentNumber(String documentNumber) {
        if (CharSequenceUtil.isBlank(documentNumber) || documentNumber.length() < 8) {
            return documentNumber;
        }
        int prefixLen = 3;
        int suffixLen = 4;
        String prefix = documentNumber.substring(0, prefixLen);
        String suffix = documentNumber.substring(documentNumber.length() - suffixLen);
        int maskLen = documentNumber.length() - prefixLen - suffixLen;
        return prefix + CharSequenceUtil.repeat("*", maskLen) + suffix;
    }

    /**
     * 构建证件信息列表
     */
    private List<AgencyFrequentQueryVo.DocumentInfo> buildDocumentList(String agentPassengerId) {
        List<MnjxAgentPassengerDocument> documents = iMnjxAgentPassengerDocumentService.lambdaQuery()
                .eq(MnjxAgentPassengerDocument::getAgentPassengerId, agentPassengerId)
                .list();

        return documents.stream().map(doc -> {
            AgencyFrequentQueryVo.DocumentInfo docInfo = new AgencyFrequentQueryVo.DocumentInfo();
            docInfo.setDocumentTypeCode(doc.getDocumentTypeCode());
            docInfo.setDocumentNumber(doc.getDocumentNumber());
            docInfo.setDocumentNumberDes(this.maskDocumentNumber(doc.getDocumentNumber()));
            docInfo.setExpiryDate(doc.getExpiryDate());
            docInfo.setIssueCountryCode(doc.getIssueCountryCode());
            docInfo.setNationalityCode(doc.getNationalityCode());
            docInfo.setIssueDate(doc.getIssueDate());
            return docInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 构建备注信息列表
     */
    private List<AgencyFrequentQueryVo.RemarkInfo> buildRemarkList(String agentPassengerId) {
        List<MnjxAgentPassengerRemark> remarks = iMnjxAgentPassengerRemarkService.lambdaQuery()
                .eq(MnjxAgentPassengerRemark::getAgentPassengerId, agentPassengerId)
                .orderByAsc(MnjxAgentPassengerRemark::getSequenceNumber)
                .list();

        if (remarks.isEmpty()) {
            return new ArrayList<>();
        }

        return remarks.stream().map(remark -> {
            AgencyFrequentQueryVo.RemarkInfo remarkInfo = new AgencyFrequentQueryVo.RemarkInfo();
            remarkInfo.setSequenceNumber(remark.getSequenceNumber());
            remarkInfo.setRemark(remark.getRemark());
            return remarkInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 构建常旅客卡信息列表
     */
    private List<AgencyFrequentQueryVo.CardInfo> buildCardList(String agentPassengerId) {
        List<MnjxAgentPassengerCard> cards = iMnjxAgentPassengerCardService.lambdaQuery()
                .eq(MnjxAgentPassengerCard::getAgentPassengerId, agentPassengerId)
                .list();

        if (cards.isEmpty()) {
            return new ArrayList<>();
        }

        return cards.stream().map(card -> {
            AgencyFrequentQueryVo.CardInfo cardInfo = new AgencyFrequentQueryVo.CardInfo();
            cardInfo.setAirlineCode(card.getAirlineCode());
            cardInfo.setFrequentFlyerNumber(card.getFrequentFlyerNumber());
            return cardInfo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteAgencyFrequent(AgencyFrequentDeleteDto dto) throws SguiResultException {
        // 1. 参数验证
        if (dto.getCsmIdentifier() == null) {
            throw new SguiResultException("客户编号不能为空");
        }

        // 2. 获取当前登录用户的工作号ID
        String currentSiId = this.getCurrentSiId();

        // 3. 查询代理常客主表数据
        MnjxAgentPassenger agentPassenger = iMnjxAgentPassengerService.lambdaQuery()
                .eq(MnjxAgentPassenger::getSiId, currentSiId)
                .eq(MnjxAgentPassenger::getCsmIdentifier, dto.getCsmIdentifier())
                .one();

        if (agentPassenger == null) {
            throw new SguiResultException("未找到指定的代理常客记录");
        }

        String agentPassengerId = agentPassenger.getAgentPassengerId();

        // 4. 删除关联表数据
        this.deleteRelatedData(agentPassengerId);

        // 5. 删除主表数据
        boolean deleteResult = iMnjxAgentPassengerService.removeById(agentPassengerId);
        if (!deleteResult) {
            throw new SguiResultException("删除代理常客主表数据失败");
        }

        log.info("成功删除代理常客，ID：{}，客户编号：{}", agentPassengerId, dto.getCsmIdentifier());

        return "success";
    }

    /**
     * 删除关联表数据
     */
    private void deleteRelatedData(String agentPassengerId) {
        // 删除证件信息
        iMnjxAgentPassengerDocumentService.lambdaUpdate()
                .eq(MnjxAgentPassengerDocument::getAgentPassengerId, agentPassengerId)
                .remove();

        // 删除备注信息
        iMnjxAgentPassengerRemarkService.lambdaUpdate()
                .eq(MnjxAgentPassengerRemark::getAgentPassengerId, agentPassengerId)
                .remove();

        // 删除常旅客卡信息
        iMnjxAgentPassengerCardService.lambdaUpdate()
                .eq(MnjxAgentPassengerCard::getAgentPassengerId, agentPassengerId)
                .remove();

        log.info("成功删除代理常客关联数据，agentPassengerId：{}", agentPassengerId);
    }
}
